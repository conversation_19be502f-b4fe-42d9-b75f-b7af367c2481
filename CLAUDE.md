# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

Cube1 Group 是一个现代化的全栈网格数据可视化系统，专注于高性能 33x33 网格（1089 个单元格）的实时渲染和交互。项目已经使用最新的前后端技术栈进行了完整的架构重构。

## 架构

### Monorepo 结构
- **Turbo 2.3.0** + **pnpm 9.15.0** 工作区管理
- **前端**: Next.js 15.1.0 + TypeScript 5.8.3 + React 18.3.1
- **后端**: FastAPI 0.116.1 + Python 3.11+ + Poetry
- **状态管理**: Zustand 5.0.6（替代了 80+ 个 useState 钩子）
- **样式**: Tailwind CSS 3.4.17

### 核心架构组件

#### 前端矩阵引擎
- **MatrixCore.ts**: 具有可配置业务模式的中央处理引擎
- **MatrixStore.ts**: 基于 Zustand 的状态管理，支持持久化和计算属性
- **MatrixTypes.ts**: 支持数据驱动渲染的统一类型系统
- **33x33 网格**: 1089 个单元格同时渲染，无需虚拟化

#### 业务模式
系统支持 4 种不同的渲染模式：
- **坐标模式**: 在每个单元格中显示 x,y 坐标
- **颜色模式**: 显示 9 种颜色类型（包括黑色）的颜色数据
- **数值模式**: 显示具有不同样式的数值
- **词语模式**: 在单元格中显示文本内容

#### 数据架构
- **A 组数据**: 预优化的矩阵数据集，包含 A-M 组
- **颜色系统**: 9 种颜色（黑色、红色、青色、黄色、紫色、橙色、绿色、蓝色、粉色）
- **层级系统**: 4 层级结构（Level 1-4）
- **混合存储**: LocalStorage + API 同步

## 开发命令

### 根目录级别 (Monorepo)
```bash
# 安装所有依赖
pnpm install

# 开发
pnpm run dev              # 使用 Turbo 启动所有应用
pnpm run build            # 构建所有应用
pnpm run lint             # 检查所有应用
pnpm run test             # 测试所有应用
pnpm run type-check       # 类型检查所有应用

# 格式化
pnpm run format           # 使用 Prettier 格式化代码
pnpm run format:check     # 检查格式化

# 应用特定
pnpm run dev:frontend     # 仅启动前端
pnpm run build:frontend   # 仅构建前端
pnpm run test:frontend    # 仅测试前端
pnpm run dev:backend      # 仅启动后端
```

### 前端开发 (apps/frontend)
```bash
cd apps/frontend

# 开发
pnpm run dev              # 在端口 4096 启动开发服务器
pnpm run build            # 构建生产版本
pnpm run start            # 启动生产服务器
pnpm run lint             # 运行 ESLint
pnpm run lint:fix         # 修复 ESLint 问题
pnpm run type-check       # 运行 TypeScript 类型检查
pnpm run format           # 使用 Prettier 格式化
pnpm run clean            # 清理 .next 目录
```

### 后端开发 (apps/backend)
```bash
cd apps/backend

# 开发
poetry install            # 安装依赖
poetry run uvicorn app.main:app --reload  # 在端口 8000 启动开发服务器

# 测试和质量
poetry run pytest         # 运行测试
poetry run ruff check     # 运行 linter
poetry run black .        # 格式化代码
poetry run mypy .         # 类型检查
```

## 关键技术细节

### 状态管理模式
- **单一数据源**: 所有矩阵状态通过 Zustand store 管理
- **计算属性**: 缓存计算以提高性能优化
- **持久化**: 自动 LocalStorage 备份，支持 Map/Set 序列化
- **性能**: 与 useState 方法相比减少 97% 的重渲染

### 矩阵数据结构
```typescript
interface MatrixData {
  cells: Map<string, CellData>;        // key: "x,y"
  selectedCells: Set<string>;          // 选中的单元格键
  hoveredCell: string | null;          // 当前悬停的单元格键
  focusedCell: string | null;          // 当前聚焦的单元格键
}
```

### 性能优化
- **React.memo**: 应用于 15 个组件以优化渲染
- **useMemo/useCallback**: 广泛缓存计算和事件处理器
- **批量更新**: 通过 Immer 进行高效的批量单元格更新
- **预建索引**: A 组数据中的优化坐标查找

### 后端 API 结构
- **FastAPI 框架**: 自动生成的 API 文档
- **模块化架构**: 不同业务功能的独立模块
- **错误处理**: 具有自定义错误类型的全面异常处理
- **中间件**: 请求计时、日志记录和 CORS 支持

## 文件结构约定

### 前端 (apps/frontend)
```
apps/frontend/
├── app/                    # Next.js 应用目录
├── components/             # React 组件
├── core/                   # 核心业务逻辑
│   ├── data/              # 矩阵数据定义
│   └── matrix/            # 矩阵引擎 (Core, Store, Types)
├── public/                # 静态资源
└── styles/                # 全局样式
```

### 后端 (apps/backend)
```
apps/backend/
├── app/
│   ├── api/               # API 路由
│   ├── core/              # 核心工具
│   ├── crud/              # 数据库操作
│   ├── models/            # 数据模型
│   ├── services/          # 业务逻辑
│   └── tasks/             # 后台任务
```

## 开发指南

### 代码风格
- **TypeScript**: 启用严格模式，全面的类型定义
- **React**: 函数式组件配合 Hooks，memo 优化
- **Zustand**: 所有状态使用单一 store，避免共享状态使用 useState
- **Python**: Black 格式化器，Ruff linter，MyPy 类型检查

### 性能考虑
- **矩阵渲染**: 所有 1089 个单元格同时渲染 - 相应地进行优化
- **状态更新**: 多个单元格更改使用批量更新
- **内存管理**: 切换模式时清除缓存
- **组件记忆化**: 对网格组件广泛使用 React.memo

### 测试策略
- **前端**: 使用 React Testing Library 进行组件测试
- **后端**: pytest 支持异步和覆盖率报告
- **E2E**: Playwright 进行完整集成测试
- **性能**: 关键渲染操作的基准测试

## 环境设置

### 前置要求
- **Node.js**: 18+ (前端)
- **Python**: 3.11+ (后端)
- **pnpm**: 9.15.0+ (包管理器)
- **Poetry**: Python 依赖管理

### 快速开始
```bash
# 克隆和设置
git clone <repository>
cd cube1_group
pnpm install

# 启动开发
pnpm run dev

# 访问应用
# 前端: http://localhost:4096
# 后端 API: http://localhost:8000/docs
```

## 重要说明

- **未找到 .cursorrules 或 Copilot 指令** - 遵循既定模式
- **矩阵数据是性能关键的** - 为 1089 个单元格优化所有操作
- **仅通过 Zustand 进行状态管理** - 避免矩阵数据使用 useState
- **TypeScript 严格模式** - 维护全面的类型覆盖
- **代码中的中文注释** - 保留现有文档语言